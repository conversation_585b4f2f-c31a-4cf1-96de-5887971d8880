
export type MediaType = "movie" | "series" | "requested";

export interface Episode {
  id: string;
  title: string;
  season: number;
  episode: number;
  description: string;
  videoLink: string;
  runtime?: string;
  airDate?: string;
  thumbnailUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Season {
  id: string;
  seasonNumber: number;
  title?: string;
  description?: string;
  episodes: Episode[];
  posterUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MediaItem {
  id: string;
  title: string;
  description: string;
  year: number;
  genres: string[];
  type: MediaType;
  image: string;
  coverImage: string; // for large carousel background
  createdAt: string; // ISO date string for when content was added

  // Enhanced fields for comprehensive content management
  tmdbId?: string;
  languages?: string[];
  posterUrl?: string;
  thumbnailUrl?: string;
  videoLinks?: string;
  quality?: string[];
  tags?: string;
  imdbRating?: string;
  runtime?: string;
  studio?: string;
  audioTracks?: string[];
  trailer?: string;
  subtitleUrl?: string;
  isPublished?: boolean;
  isFeatured?: boolean;
  addToCarousel?: boolean;
  updatedAt?: string;

  // Web series specific fields
  seasons?: Season[];
  totalSeasons?: number;
  totalEpisodes?: number;
}

// Enhanced content item interface for admin management
export interface ContentItem {
  id: string;
  title: string;
  type: string;
  year: number;
  genre: string[];
  status: string;
  featured: boolean;
  carousel: boolean;
  imdbRating: string;
  runtime: string;
  studio: string;
  description?: string;
  posterUrl?: string;
  thumbnailUrl?: string;
  videoLinks?: string;
  languages?: string[];
  quality?: string[];
  tags?: string;
  audioTracks?: string[];
  trailer?: string;
  subtitleUrl?: string;
  seasons?: Season[];
  createdAt: string;
  updatedAt: string;
}
