
import { useState, useEffect } from "react";
import AddTitleForm from "@/components/admin/AddTitleForm";
import ContentManager from "@/components/admin/ContentManager";
import EnhancedContentManager from "@/components/admin/EnhancedContentManager";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Plus, Settings, ArrowUp } from "lucide-react";
import { Link } from "react-router-dom";
import { scrollToTop } from "@/utils/scrollToTop";

export default function AdminPanel() {
  const [showBackToTop, setShowBackToTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col">
      <Header />
      
      <main className="flex-1 p-0 bg-background/95 text-foreground flex flex-col items-center w-full">
        <div className="w-full max-w-7xl mx-auto pt-6 sm:pt-10 pb-12 px-4 relative">
          {/* Back to Home Button - Responsive positioning */}
          <div className="absolute top-2 right-2 sm:top-4 sm:right-4 z-10">
            <Link to="/" onClick={scrollToTop}>
              <Button variant="outline" size="sm" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-4 py-1 sm:py-2">
                <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden xs:inline">Back to Home</span>
                <span className="xs:hidden">Back</span>
              </Button>
            </Link>
          </div>

          {/* Back to Top Button - Fixed position at bottom right */}
          {showBackToTop && (
            <div className="fixed bottom-2 right-2 sm:bottom-4 sm:right-4 z-10">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={scrollToTop}
                className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-4 py-1 sm:py-2 shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <ArrowUp className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden xs:inline">Back to Top</span>
                <span className="xs:hidden">Top</span>
              </Button>
            </div>
          )}
          
          <div className="pt-12 sm:pt-0">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 text-primary text-center">Admin Panel</h1>
            <p className="text-muted-foreground mb-6 sm:mb-8 text-center max-w-xl mx-auto text-sm sm:text-base px-4">
              Manage movie & series entries directly or via TMDB. Add, edit, and preview new content to keep your streaming catalog up to date.
            </p>

            <Tabs defaultValue="add-content" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6 sm:mb-8">
                <TabsTrigger value="add-content" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
                  <Plus className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">Add New Content</span>
                  <span className="sm:hidden">Add New</span>
                </TabsTrigger>
                <TabsTrigger value="manage-content" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm">
                  <Settings className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">Manage Existing Content</span>
                  <span className="sm:hidden">Manage</span>
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="add-content" className="space-y-6">
                <AddTitleForm />
              </TabsContent>
              
              <TabsContent value="manage-content" className="space-y-6">
                <EnhancedContentManager />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
